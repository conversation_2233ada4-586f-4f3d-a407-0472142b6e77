// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    
    // 平滑滚动效果
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 鼠标跟随效果
    let mouseX = 0;
    let mouseY = 0;
    let cursorX = 0;
    let cursorY = 0;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    // 创建自定义光标
    const cursor = document.createElement('div');
    cursor.className = 'custom-cursor';
    cursor.style.cssText = `
        position: fixed;
        width: 20px;
        height: 20px;
        background: radial-gradient(circle, rgba(255,107,107,0.8) 0%, rgba(78,205,196,0.8) 100%);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        transition: transform 0.1s ease;
        mix-blend-mode: difference;
    `;
    document.body.appendChild(cursor);

    function updateCursor() {
        cursorX += (mouseX - cursorX) * 0.1;
        cursorY += (mouseY - cursorY) * 0.1;
        cursor.style.left = cursorX - 10 + 'px';
        cursor.style.top = cursorY - 10 + 'px';
        requestAnimationFrame(updateCursor);
    }
    updateCursor();

    // 按钮点击效果
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // 创建波纹效果
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // 添加波纹动画CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // 滚动时的视差效果
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.particles');
        const speed = scrolled * 0.5;
        
        if (parallax) {
            parallax.style.transform = `translateY(${speed}px)`;
        }
    });

    // 卡片悬停效果
    document.querySelectorAll('.feature-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 浮动操作按钮功能
    const fabButton = document.querySelector('.fab-button');
    if (fabButton) {
        fabButton.addEventListener('click', function() {
            // 创建菜单选项
            const menu = document.createElement('div');
            menu.className = 'fab-menu';
            menu.style.cssText = `
                position: fixed;
                bottom: 90px;
                right: 2rem;
                display: flex;
                flex-direction: column;
                gap: 10px;
                z-index: 999;
            `;
            
            const options = [
                { icon: '🏠', text: '首页' },
                { icon: '📧', text: '联系' },
                { icon: '⚙️', text: '设置' }
            ];
            
            options.forEach((option, index) => {
                const menuItem = document.createElement('div');
                menuItem.style.cssText = `
                    width: 50px;
                    height: 50px;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transform: scale(0);
                    animation: popIn 0.3s ease forwards;
                    animation-delay: ${index * 0.1}s;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                `;
                menuItem.textContent = option.icon;
                menuItem.title = option.text;
                menu.appendChild(menuItem);
            });
            
            document.body.appendChild(menu);
            
            // 点击其他地方关闭菜单
            setTimeout(() => {
                document.addEventListener('click', function closeMenu(e) {
                    if (!menu.contains(e.target) && e.target !== fabButton) {
                        menu.remove();
                        document.removeEventListener('click', closeMenu);
                    }
                });
            }, 100);
        });
    }

    // 添加弹出动画CSS
    const popInStyle = document.createElement('style');
    popInStyle.textContent = `
        @keyframes popIn {
            to {
                transform: scale(1);
            }
        }
    `;
    document.head.appendChild(popInStyle);

    // 页面加载动画
    const animateOnLoad = () => {
        const elements = document.querySelectorAll('.hero-content > *');
        elements.forEach((el, index) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                el.style.transition = 'all 0.8s ease';
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 200);
        });
    };

    // 执行加载动画
    setTimeout(animateOnLoad, 100);

    // 添加键盘快捷键
    document.addEventListener('keydown', (e) => {
        // 按ESC键回到顶部
        if (e.key === 'Escape') {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        // 按空格键暂停/播放动画
        if (e.code === 'Space' && e.target === document.body) {
            e.preventDefault();
            const particles = document.querySelectorAll('.particle');
            particles.forEach(particle => {
                if (particle.style.animationPlayState === 'paused') {
                    particle.style.animationPlayState = 'running';
                } else {
                    particle.style.animationPlayState = 'paused';
                }
            });
        }
    });

    // 性能优化：节流滚动事件
    let ticking = false;
    function updateOnScroll() {
        if (!ticking) {
            requestAnimationFrame(() => {
                // 滚动相关的更新
                const scrolled = window.pageYOffset;
                const header = document.querySelector('.header');
                
                if (scrolled > 100) {
                    header.style.background = 'rgba(255, 255, 255, 0.15)';
                } else {
                    header.style.background = 'rgba(255, 255, 255, 0.1)';
                }
                
                ticking = false;
            });
            ticking = true;
        }
    }

    window.addEventListener('scroll', updateOnScroll);

    console.log('🚀 酷炫页面已加载完成！');
    console.log('💡 提示：按ESC键回到顶部，按空格键暂停/播放动画');
});
